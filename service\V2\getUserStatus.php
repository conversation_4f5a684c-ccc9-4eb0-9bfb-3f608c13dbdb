<?php

include("../../includes/config.php");
include("../../includes/commonfun.php");
include("../../class/clsDB.php");
include("../../class/clsStudent.php");
include('../../class/clsCountryStateMaster.php');
include("../../class/clsStatusCodes.php");
include("../../class/clsClinician.php");
include('validateParameters.php');

$isTrue = true;
$isFalse = false;
$objStatusCode = new clsStatusCodes();


// Check if the request method 
if ($_SERVER['REQUEST_METHOD'] === 'GET') {

	$UserId = "";
	$AccessToken = '';
	$errroMessage = "";
	if (!isset($_GET['UserType']) || ($_GET['UserType'] == '')) {
		CreateResponce($objStatusCode::HTTP_NOT_FOUND, $isFalse, 'Please pass the user type');
		exit();
	}

	$userType = isset($_GET['UserType']) ? $_GET['UserType'] : 0;
	$LoggedUserType = ($userType) ? 'Clinician' : 'Student';

	$requestParameters = array(
		'UserId' => isset($_GET['UserId']) ? $_GET['UserId'] : 0,
		'AccessToken' => isset($_GET['AccessToken']) ? $_GET['AccessToken'] : '',
	);

	// Passarry for Vaildation
	$validatedParameters = validateParameters($requestParameters, $objStatusCode, $isFalse, $isTrue);

	// Access the validated parameters
	if (isset($validatedParameters['UserId']) || isset($validatedParameters['AccessToken'])) {
		$UserId = $validatedParameters['UserId'];
		$accesstoken = $validatedParameters['AccessToken'];
	}

	//-----------------------------------
	//Validate AccessToken
	//-----------------------------------
	if (ValidateUserAccessToken($UserId, $accesstoken) == false) {
		CreateResponce($objStatusCode::HTTP_UNAUTHORIZED, $isFalse, 'You are not authorized user to use this service');
		exit();
	}

	//-----------------------------------
	// Assuming $userType is set based on the condition
	//-----------------------------------
	$objUser = $userType ? new clsClinician() : new clsStudent();
	$getUserDetailsMethod = $userType ? 'GetClinicianDetails' : 'GetStudentDetails';
	$rowUser = $objUser->$getUserDetailsMethod($UserId);

	if ($rowUser == '') {
		CreateResponce($objStatusCode::HTTP_UNAUTHORIZED, $isFalse, 'Invalid User');
		exit();
	}

	$SchoolId = $rowUser['schoolId'];
	$LoggedUserRole = $userType ? stripslashes($rowUser['title']) : $rowUser['ranktitle'];
	$LoggedClinicianType = isset($rowUser['loggedClinicianType']) ? stripslashes($rowUser['loggedClinicianType']) : '';
	$LoggedUserFirstName = stripslashes($rowUser['firstName']);
	$LoggedUserMiddleName = stripslashes($rowUser['middleName']);
	$LoggedUserLastName = stripslashes($rowUser['lastName']);
	$LoggedUserEmail = stripslashes($rowUser['email']);
	$LoggedUserName = stripslashes($rowUser['username']);

	$objDB = new clsDB();
	$isChat = $objDB->GetSingleColumnValueFromTable('schoolsettings', 'status', 'schoolId', $SchoolId, 'type', 'chat');

	// echo $isChat;exit;

	if ($isChat)
		$chat = $isTrue;
	else
		$chat = $isFalse;

	if ($userType == 0) {
		$LoggedUserAddress1 = stripslashes($rowUser['address1']);
		$LoggedUserAddress2 = stripslashes($rowUser['address2']);

		$stateId = ($rowUser['stateId']);
		$city = stripslashes($rowUser['city']);
		$zipCode = stripslashes($rowUser['zip']);
		//CountryState
		$objCountryStateMaster = new clsCountryStateMaster();
		$countryId = $objCountryStateMaster->GetParentIdFromChildId($stateId);
		$countryName = $objCountryStateMaster->GetLocationName($countryId);
		$stateName = $objCountryStateMaster->GetLocationName($stateId);
		unset($objCountryStateMaster);
	}

	// User Profile Image
	$LoggedUserProfileImagePath = stripslashes($rowUser['profilePic']);
	$LoggedUserProfileImagePath = $userType ?
		GetClinicianImagePath($UserId, $SchoolId, $LoggedUserProfileImagePath) :
	GetStudentImagePath($UserId, $SchoolId, $LoggedUserProfileImagePath);
	$LoggedUserProfileImagePath .= '?rand=' . rand(1, 100);

	unset($objUser);


	$userDetails = array(
		"LoggedUserId" => $UserId,
		"AccessToken" => $accesstoken,
		"LoggedUserFirstName" => $LoggedUserFirstName,
		"LoggedUserMiddleName" => $LoggedUserMiddleName,
		"LoggedUserLastName" => $LoggedUserLastName,
		"LoggedUserEmail" => $LoggedUserEmail,
		"LoggedUserName" => $LoggedUserName,
		"LoggedUserProfile" => $LoggedUserProfileImagePath,
		"chat"=>$chat

	);

	if ($userType) {
		$userDetails["LoggedUserRole"] = $LoggedUserRole;
		$userDetails["loggedClinicianType"] = $LoggedClinicianType;
	} else {
		$userDetails["LoggedUserRankTitle"] = $LoggedUserRole;
		$userDetails["LoggedUserAddress1"] = $LoggedUserAddress1;
		$userDetails["LoggedUserAddress2"] = $LoggedUserAddress2;
		$userDetails["CountryId"] = $countryId;
		$userDetails["CountryName"] = $countryName;
		$userDetails["StateId"] = $stateId;
		$userDetails["StateName"] = $stateName;
		$userDetails["City"] = $city;
		$userDetails["ZipCode"] = $zipCode;
	}

	CreateResponce($objStatusCode::HTTP_OK, $isTrue, 'Action successful.', $userDetails);
	exit();
} else {
	CreateResponce($objStatusCode::HTTP_METHOD_NOT_ALLOWED, $isFalse, 'Method not allowed');
	exit();
}
