<div class="custom-toast ">

	<div class="toast-content">
		<!-- <i class="fas fa-solid fa-check check"></i> -->
		<!-- <ion-icon name="checkmark-circle-outline" class="toast-check-icon"></ion-icon> -->
		<i class="fa fa-check-circle toast-check-icon" aria-hidden="true"></i>


		<div class="message">
			<span class="text text-1" id="Notititle"></span>
			<span class="text text-2" id="Notimessage"></span>
		</div>
	</div>
	<!-- <i class="fa-solid fa-xmark custom-toast-close"></i> -->
	<!-- <ion-icon name="close-circle-outline" class="toast-close-icon"></ion-icon> -->
	<i class="fa fa-times-circle toast-close-icon" aria-hidden="true"></i>


	<!-- Remove 'active' class, this is just to show in Codepen thumbnail -->
	<div class="chat-progress active"></div>
</div>
<div id="loading-div-background">
	<div id="loading-div" class="ui-corner-all">
		<img style="height:31px;width:31px;margin:30px;" src="<?php echo ($dynamicOrgUrl); ?>/assets/images/loader.gif"
			alt="Loading.." /><br>PROCESSING. PLEASE WAIT...
	</div>
</div>
<div id="divBottomRightLoader" style="display:none;">Working...</div>
<script type="text/javascript">
	//-------------------------------------------------------------------------------------
	//----Global variables from server-----------------------------------------------------
	//-------------------------------------------------------------------------------------
	var applicationName = '<?php echo $currenschoolDisplayname; ?>';
	//-------------------------------------------------------------------------------------
</script>

<script src="https://cdn.socket.io/4.5.4/socket.io.min.js"></script>
<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/jquery.min.js" type="text/javascript"></script>
<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/bootstrap.min.js" type="text/javascript"></script>
<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/common.js" type="text/javascript"></script>
<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/jquery.userTimeout.js"></script>
<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/mask/jquery.maskedinput.js"></script>
<script src="https://www.gstatic.com/firebasejs/10.11.0/firebase-app-compat.js"></script>
<script src="https://www.gstatic.com/firebasejs/10.11.0/firebase-messaging-compat.js"></script>

<script>
	var schoolId = '<?php echo $currentSchoolId; ?>';
	var loggedAsClinicianBackUserId = '<?php echo $loggedAsClinicianBackUserId; ?>';
	// let newWindow;

	if(loggedAsClinicianBackUserId == 0)
	{

			
		$(document).ready(function () {
		// alert('Hello');
		WebSocketConnectionStart('<?php echo EncodeQueryData($currentSchoolId); ?>','<?php echo ($clinicianEmail); ?>','<?php echo EncodeQueryData($_SESSION['loggedClinicianId']);?>',parseInt(3));	
		GetChatNotificationsList(parseInt(3), '<?php echo ($clinicianEmail); ?>', '<?php echo EncodeQueryData($currentSchoolId); ?>', '<?php echo EncodeQueryData($_SESSION['loggedClinicianId']);?>');
	});
	const firebaseConfig = {
		apiKey: "AIzaSyDNG3mPNaLwLVnMK3wJU4_rqESYXOWEELw",
		authDomain: "clinical-trac-1572429876166.firebaseapp.com",
		projectId: "clinical-trac-1572429876166",
		storageBucket: "clinical-trac-1572429876166.firebasestorage.app",
		messagingSenderId: "787683175754",
		appId: "1:787683175754:web:9ace1bbb2e0ccbb99325b3",
		measurementId: "G-CVPS8DNVFY"
	};
	
	const firebaseApp = firebase.initializeApp(firebaseConfig);
	const messaging = firebase.messaging();
	
	// Detect Browser and Handle Safari Compatibility
	function isSafari() {
		return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
	}
	
	if (isSafari()) {
		// console.log("This is Safari. Firebase Messaging not supported natively.");
		// Add logic here to use APNs or alternative for Safari
		// alert("Safari browser detected. Push notifications might not be supported.");
	}
	else {
		messaging.getToken({
			vapidKey: "BJl-IuskTx7KwBCALnqg4YSfzCftoenAWxJJMfYqh7c20hwZwdg72EqyXndahBunFCwex65xvMwnoMyuZaGcjgI"
		})
			.then((currentToken) => {
				if (currentToken) {
					console.log('clinicaltrac: Firebase Token:', currentToken);
					
					SendDeviceTokenToChatApp(currentToken, schoolId, parseInt(3), '<?php echo ($clinicianEmail); ?>', <?php echo ($_SESSION["loggedClinicianId"]); ?>, '<?php echo CHAT_APIKEY_LOCAL; ?>', '<?php echo CHAT_USER_SERVICE; ?>');
					
					
				} else {
					console.log('clinicaltrac: No registration token available.');
				}
			})
			.catch((err) => {
				console.error('clinicaltrac: Error getting token:', err);
			});

			messaging.onMessage((payload) => {
			console.log('clinicaltrac: Message received:', payload);

			// Parse the payload and extract the `uri`
			const data = JSON.parse(payload.data.payload);

			const title = data.title;
			const body = data.body;
			const uri = data.uri || ''; // Fallback URL
			
			console.log("URI:", uri);
			
			// Update UI elements
			$('#Notititle').text(title);
			$('#Notimessage').text(body);
			
			toast.classList.add("active");
			setTimeout(() => {
				toast.classList.remove("active");
			}, 5300);
			
			const notificationTitle = data.title || "New Message";
			
			const notificationOptions = {
				body: data.body || "You have a new message.",
				data: {
					url: uri // URL to redirect to
				}
			};
			
			// Show browser notification
			if (Notification.permission === 'granted') {
				const notification = new Notification(notificationTitle, notificationOptions);
				// Handle notification click event
				notification.onclick = (event) => {
					event.preventDefault(); // Prevent default action
					
					const redirectUrl = notificationOptions.data.url;
					
					if (redirectUrl && !newWindow) {

						console.log("Opening new tab:", redirectUrl);
						newWindow = window.open(redirectUrl, '_blank');
						console.log('newWindow', newWindow);
					}
					else {
						console.log('else focus on', newWindow);
						newWindow.focus();
					}
					
				};
			} else {
				console.log("clinicaltrac: Notifications are not enabled.");
			}
			
			GetChatNotificationsList(parseInt(3), '<?php echo ($clinicianEmail); ?>', '<?php echo EncodeQueryData($currentSchoolId); ?>', '<?php echo EncodeQueryData($_SESSION['loggedClinicianId']);?>');

		});
		
		function openTabChatNotification() {
			GetChatNotificationsList(parseInt(3), '<?php echo ($clinicianEmail); ?>', '<?php echo EncodeQueryData($currentSchoolId); ?>', '<?php echo EncodeQueryData($_SESSION['loggedClinicianId']);?>');
		}
	}
}
</script>
<script type="text/javascript">
	jQuery(function ($) {
		$('.dateInputFormat').mask("99/99/9999");
	});

	// document.getElementById("notification-icon").onclick = function () {
	//     location.href = "./cliniciannotification.html";
	// };
	// Check if the notification-icon element exists
	var notificationIcon = document.getElementById("notification-icon");

	if (notificationIcon !== null) {
		// If the element exists, set the click event
		notificationIcon.onclick = function () {
			// location.href = "./studentnotification.html";
			location.href = "./cliniciannotification.html";
		};
	}

	$(document).userTimeout({

		// ULR to redirect to, to log user out
		logouturl: 'logout.html',

		// URL Referer - false, auto or a passed URL     
		referer: false,

		// Name of the passed referal in the URL
		refererName: 'refer',

		// Toggle for notification of session ending
		notify: true,

		// Toggle for enabling the countdown timer
		timer: true,

		// 4 hrs in Milliseconds, then notification of logout
		session: 14400000,

		// 1 Minutes in Milliseconds, then logout
		force: 60000,

		// Model Dialog selector (auto, bootstrap, jqueryui)              
		ui: 'auto',

		// Shows alerts
		debug: false,

		modalTitle: 'Session Timeout',

		// Modal Body
		modalBody: 'You\'re being timed out due to inactivity. Please choose to stay signed in or to logoff. Otherwise, you will logged off automatically.',

		// Modal log off button text
		modalLogOffBtn: 'Log Off',

		// Modal stay logged in button text        
		modalStayLoggedBtn: 'Stay Logged In'

	});


	$(document).ready(function () {
		SetPageTitle();

		$('body').click(function (e) {
			if (e.target.id != 'notification-icon') {
				$("#notification-latest").hide();
			}
		});
	});

	function displayNotificationCount() {
		$.ajax({
			url: "<?php echo ($dynamicOrgUrl); ?>/ajax/clinician_noti_count.html",
			context: document.body,
			processData: false,
			//dataType: "json",
			success: function (totalUnreadCount) {

				//alert(totalUnreadCount);
				if (totalUnreadCount > 0) {
					$('#notification-count').text(totalUnreadCount);
					$('#webNotiCount').text(totalUnreadCount);
					// $('.notification-icon').append("<span id='notification-count' style='color:white' class='badge badge-custom'>" + totalUnreadCount + "</span>");
				} else {
					$('#webNotiCount').addClass('hide');
					//alert('');
					//$('.notification-icon').append("<span id='notification-count' style='color:white' class='badge badge-custom'>""</span>");

				}

				//$('.notification-icon').append("<span id='notification-count' style='color:white' class='badge badge-custom'>"+totalUnreadCount.notificationCount+"</span>");

			}
		});
	}
	displayNotificationCount();
</script>