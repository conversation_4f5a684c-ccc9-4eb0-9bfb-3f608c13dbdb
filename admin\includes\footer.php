<div class="custom-toast">

	<div class="toast-content">
		<!-- <i class="fas fa-solid fa-check check"></i> -->
		<!-- <ion-icon name="checkmark-circle-outline" class="toast-check-icon"></ion-icon> -->
		<i class="fa fa-pencil toast-check-icon" aria-hidden="true"></i>
		<div class="message">
			<span class="text text-1" id="Notititle"></span>
			<span class="text text-2" id="Notimessage"></span>
		</div>
	</div>
	<!-- <i class="fa-solid fa-xmark custom-toast-close"></i> -->
	<!-- <ion-icon name="close-circle-outline" class="toast-close-icon"></ion-icon> -->
	<i class="fa fa-times-circle toast-close-icon" aria-hidden="true"></i>


	<!-- Remove 'active' class, this is just to show in Codepen thumbnail -->
	<div class="chat-progress active"></div>
</div>
<div id="loading-div-background">
	<div id="loading-div" class="ui-corner-all">
		<img style="height:31px;width:31px;margin:30px;" src="<?php echo ($dynamicOrgUrl); ?>/assets/images/loader.gif"
			alt="Loading.." /><br>PROCESSING. PLEASE WAIT...
	</div>
</div>
<div id="divBottomRightLoader" style="display:none;">Working...</div>

<script type="text/javascript">
	//-------------------------------------------------------------------------------------
	//----Global variables from server-----------------------------------------------------
	//-------------------------------------------------------------------------------------
	var applicationName = '<?php echo $currenschoolDisplayname; ?>';
	//-------------------------------------------------------------------------------------
</script>

<script src="https://cdn.socket.io/4.5.4/socket.io.min.js"></script>
<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/jquery.min.js" type="text/javascript"></script>
<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/bootstrap.min.js" type="text/javascript"></script>
<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/common.js" type="text/javascript"></script>
<script src="<?php echo ($dynamicOrgUrl); ?>/assets/js/jquery.userTimeout.js"></script>
<script type="text/javascript" src="<?php echo ($dynamicOrgUrl); ?>/assets/js/mask/jquery.maskedinput.js"></script>
<script src="https://www.gstatic.com/firebasejs/10.11.0/firebase-app-compat.js"></script>
<script src="https://www.gstatic.com/firebasejs/10.11.0/firebase-messaging-compat.js"></script>

<script>
	var isSuperAdmin = '<?php echo $isCurrentSchoolSuperAdmin; ?>';
	var schoolId = '<?php echo $currentSchoolId; ?>';
	var loggedAsBackUserId = '<?php echo $loggedAsBackUserId; ?>';
	// console.log("loggedAsBackUserId ", loggedAsBackUserId);

	var chatroleId = (isSuperAdmin == 1) ? parseInt(1) : parseInt(2);
	schoolId = (isSuperAdmin == 1) ? 0 : schoolId;

	if (isSuperAdmin != 1 && loggedAsBackUserId == 0) {

		$(document).ready(function () {

			// alert('Hello');
			WebSocketConnectionStart('<?php echo EncodeQueryData($currentSchoolId); ?>','<?php echo ($_SESSION["loggedUserEmail"]); ?>','<?php echo EncodeQueryData($_SESSION['loggedUserId']); ?>',chatroleId); //
			GetChatNotificationsList(chatroleId, '<?php echo ($_SESSION["loggedUserEmail"]); ?>', '<?php echo EncodeQueryData($currentSchoolId); ?>', '<?php echo EncodeQueryData($_SESSION['loggedUserId']); ?>');
		});

		// Initialize Firebase Messaging

		// For Firebase JS SDK v7.20.0 and later, measurementId is optional
		const firebaseConfig = {
			apiKey: "AIzaSyDNG3mPNaLwLVnMK3wJU4_rqESYXOWEELw",
			authDomain: "clinical-trac-1572429876166.firebaseapp.com",
			projectId: "clinical-trac-1572429876166",
			storageBucket: "clinical-trac-1572429876166.firebasestorage.app",
			messagingSenderId: "787683175754",
			appId: "1:787683175754:web:9ace1bbb2e0ccbb99325b3",
			measurementId: "G-CVPS8DNVFY"
		};

		const firebaseApp = firebase.initializeApp(firebaseConfig);
		const messaging = firebase.messaging();

		// Detect Browser and Handle Safari Compatibility
		function isSafari() {
			return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);
		}

		if (isSafari()) {
			// console.log("This is Safari. Firebase Messaging not supported natively.");
			// Add logic here to use APNs or alternative for Safari
			// alert("Safari browser detected. Push notifications might not be supported.");
		}
		else {

			messaging.getToken({
				vapidKey: "BJl-IuskTx7KwBCALnqg4YSfzCftoenAWxJJMfYqh7c20hwZwdg72EqyXndahBunFCwex65xvMwnoMyuZaGcjgI"
			})
				.then((currentToken) => {
					if (currentToken) {
						console.log('clinicaltrac: Firebase Token:', currentToken);

						SendDeviceTokenToChatApp(currentToken, schoolId, chatroleId, '<?php echo ($_SESSION["loggedUserEmail"]); ?>', <?php echo ($_SESSION["loggedUserId"]); ?>, '<?php echo CHAT_APIKEY_LOCAL; ?>', '<?php echo CHAT_USER_SERVICE; ?>');


					} else {
						console.log('clinicaltrac: No registration token available.');
					}
				})
				.catch((err) => {
					console.error('clinicaltrac: Error getting token:', err);
				});

			// let newWindow;

			messaging.onMessage((payload) => {

				console.log('clinicaltrac: Message received:', payload);

				// Parse the payload and extract the `uri`
				const data = JSON.parse(payload.data.payload);

				const title = data.title;
				const body = data.body;
				const uri = data.uri || ''; 

				console.log("URI:", uri);

				// Update UI elements
				$('#Notititle').text(title);
				$('#Notimessage').text(body);

				toast.classList.add("active");
				setTimeout(() => {
					toast.classList.remove("active");
				}, 5300);

				const notificationTitle = data.title || "New Message";
			
				const notificationOptions = {
					body: data.body || "You have a new message.",
					data: {
						url: uri // URL to redirect to
					}
				};

				// Show browser notification
				if (Notification.permission === 'granted') {
					const notification = new Notification(notificationTitle, notificationOptions);
					// Handle notification click event
					notification.onclick = (event) => {
						event.preventDefault(); // Prevent default action

						const redirectUrl = notificationOptions.data.url;

						if (redirectUrl && !newWindow) {

							console.log("Opening new tab:", redirectUrl);
							newWindow = window.open(redirectUrl, '_blank');
							console.log('newWindow', newWindow);
						}
						else {
							console.log('else focus on', newWindow);
							newWindow.focus();
						}

					};
				} else {
					console.log("clinicaltrac: Notifications are not enabled.");
				}

				GetChatNotificationsList(chatroleId, '<?php echo ($_SESSION["loggedUserEmail"]); ?>', '<?php echo EncodeQueryData($currentSchoolId); ?>', '<?php echo EncodeQueryData($_SESSION["loggedUserId"]); ?>');
			});


			function openTabChatNotification() {
				// console.log('openTabChatNotification');
				GetChatNotificationsList(chatroleId, '<?php echo ($_SESSION["loggedUserEmail"]); ?>', '<?php echo EncodeQueryData($currentSchoolId); ?>', '<?php echo EncodeQueryData($_SESSION["loggedUserId"]); ?>');
			}

		}
	}

</script>

<script type="text/javascript">
	jQuery(function ($) {
		$('.dateInputFormat').mask("99/99/9999");
	});


	var notificationIcon = document.getElementById("notification-icon");

	if (notificationIcon !== null) {

		document.getElementById("notification-icon").onclick = function () {

			if (isSuperAdmin == 1)
				location.href = "./superadminnotification.html";
			else
				location.href = "./adminnotification.html";
		};
	}

	$(document).userTimeout({

		// ULR to redirect to, to log user out
		logouturl: 'logout.html',

		// URL Referer - false, auto or a passed URL     
		referer: false,

		// Name of the passed referal in the URL
		refererName: 'refer',

		// Toggle for notification of session ending
		notify: true,

		// Toggle for enabling the countdown timer
		timer: true,

		// 4 hours in Milliseconds, then notification of logout
		session: 14400000,
		//session: 120000,                   

		// 1 Minutes in Milliseconds, then logout
		force: 60000,

		// Model Dialog selector (auto, bootstrap, jqueryui)              
		ui: 'auto',

		// Shows alerts
		debug: false,

		modalTitle: 'Session Timeout',

		// Modal Body
		modalBody: 'You\'re being timed out due to inactivity. Please choose to stay signed in or to logoff. Otherwise, you will logged off automatically.',

		// Modal log off button text
		modalLogOffBtn: 'Log Off',

		// Modal stay logged in button text        
		modalStayLoggedBtn: 'Stay Logged In'

	});

	$(window).bind("pageshow", function (event) {
		if (event.originalEvent.persisted) {
			window.location.reload();
		}
	});
	$(document).ready(function () {

		

		SetPageTitle();

		$('body').click(function (e) {
			if (e.target.id != 'notification-icon') {
				$("#notification-latest").hide();
			}
		});
	});


	function displayNotificationCount() {
		// alert("web Notification count");
		var schoolId = '<?php echo $currentSchoolId; ?>';

		var isSuperAdmin = '<?php echo $isCurrentSchoolSuperAdmin; ?>';

		if (isSuperAdmin == 1)
			ajaxUrl = "<?php echo ($dynamicOrgUrl); ?>/ajax/superadmin_noti_count.html";
		else
			ajaxUrl = "<?php echo ($dynamicOrgUrl); ?>/ajax/admin_noti_count.html";


		$.ajax({
			type: "POST",
			url: ajaxUrl,
			data: {
				schoolId: schoolId
			},

			//dataType: "json",
			success: function (totalUnreadCount) {

				// alert(totalUnreadCount);
				if (isSuperAdmin == 1 && totalUnreadCount > 0) {

					$('.notification-icon').append("<span id='notification-count' style='color:white' class='badge badge-custom'>" + totalUnreadCount + "</span>");
				}
				else {

					if (totalUnreadCount > 0) {
						$('#notification-count').text(totalUnreadCount);
						$('#webNotiCount').text(totalUnreadCount);
					} else {
						$('#webNotiCount').addClass('hide');

						//alert('');
						//$('.notification-icon').append("<span id='notification-count' style='color:white' class='badge badge-custom'>""</span>");

					}
				}


				//$('.notification-icon').append("<span id='notification-count' style='color:white' class='badge badge-custom'>"+totalUnreadCount.notificationCount+"</span>");

			}
		});
	}
	displayNotificationCount();
</script>